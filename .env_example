export DEBUG=True
export LOG_LEVEL=ERROR
export X-API-Key=xkeNjW8urWmSVKZmYPSyH8Pz6d24KwSiCSewJy8vKdm7KNyfCV

# Database
export DB_SERVICE=MCDC
export DB_USER=sa
export DB_PASSWORD=test12345*
export DB_HOST=************
export DB_PORT=4333
export BASE_URL=************
export MEDIA_PREFIX=files/
export UPLOAD_DIR=uploads/
export CHAT_SUB_DIR=chat/
export PAYMENT_SUB_DIR=payment/
export DOCUMENT_SUB_DIR=docs/
export DASHBOARD_SUB_DIR=uploads/images/
export WORKLIST_SUB_DIR=worklist/
export MATCHING_SUB_DIR=matching/
export BASE_FILE_URL=https://localhost:8080/

export SMTP_SERVER = "smtp.gmail.com"
export SMTP_PORT = 587  # TLS Port
export SMTP_USERNAME = ""
export SMTP_PASSWORD = ""

export WEBSOCKET_HOST=localhost
export WEBSOCKET_PORT=8080
export WEBSOCKET_PROTOCOL=ws

# JWT Token Configuration
export JWT_ACCESS_TOKEN_MINUTES=15
export JWT_REFRESH_TOKEN_DAYS=1

export THAID_BASE_AUTH_URL=https://imauth.bora.dopa.go.th/api/v2/oauth2/
export THAID_CALLBACK_URL=https://pdmoapi.wewasanad.org/api/callback/thaid
export THAID_CLIENT_ID=
export THAID_CLIENT_SECRET=

export JWT_PRIVATE_KEY_PATH=
export JWT_PUBLIC_KEY_PATH=